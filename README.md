# Ttools - 图片尺寸转换工具

一个简单易用的图片尺寸转换工具，支持多种尺寸转换和Favicon生成。

## ✨ 功能特性

### 📏 图片尺寸转换
- **28×28 像素** - 小图标
- **108×108 像素** - 中等图标  
- **320×320 像素** - 大图标/头像 ⭐ **新增**

### 🌟 Favicon 生成
- 自动生成包含多种尺寸的 `.ico` 文件
- 支持 16×16、32×32、48×48 像素
- 符合Web标准，兼容各种浏览器

### 🎨 界面特色
- 美观的渐变UI设计
- 响应式布局
- 中文友好界面
- 实时预览功能

## 🚀 快速开始

### 环境要求
- Python 3.7+
- Flask 3.0.0
- Pillow 10.4.0

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python px.py
```

访问 `http://127.0.0.1:5000` 开始使用

## 📁 项目结构
```
Ttools/
├── px.py              # 主应用文件
├── requirements.txt   # 依赖文件
├── templates/         # HTML模板
│   ├── index.html    # 主页面
│   └── result.html   # 结果页面
├── static/           # 静态文件目录
├── temp/            # 临时文件目录
└── README.md        # 项目说明
```

## 🔧 使用说明

### 图片尺寸转换
1. 选择要转换的目标尺寸（可多选）
2. 上传图片文件（支持 PNG、JPG、JPEG）
3. 点击"上传并调整尺寸"
4. 在结果页面下载转换后的图片

### Favicon 生成
1. 上传图片文件
2. 点击"生成Favicon"
3. 下载生成的 `.ico` 文件

## 🛠️ 技术栈
- **后端**: Flask (Python)
- **图片处理**: Pillow
- **前端**: HTML5 + CSS3
- **UI框架**: 自定义响应式设计

## 📝 更新日志

### v1.0.0 (2024-06-12)
- ✅ 支持三种尺寸转换：28×28、108×108、320×320像素
- ✅ 新增320×320尺寸，适用于大图标和头像
- ✅ Favicon生成功能（16×16、32×32、48×48）
- ✅ 美观的渐变UI设计
- ✅ 自动文件清理机制
- ✅ 支持PNG、JPG、JPEG格式

## 📄 许可证
MIT License

## 🤝 贡献
欢迎提交 Issue 和 Pull Request！

---
⭐ 如果这个项目对您有帮助，请给个星标支持一下！
