from flask import Flask, request, send_file, render_template, url_for, session, redirect
from PIL import Image
import os
from io import BytesIO
import uuid
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key'

# 预设的三个尺寸
SIZES = [(28, 28), (108, 108), (320, 320)]
# favicon尺寸
FAVICON_SIZES = [(16, 16), (32, 32), (48, 48)]
# 添加临时文件存储路径
UPLOAD_FOLDER = 'temp'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

@app.route('/')
def index():
    return render_template('index.html', sizes=SIZES)  # 注意这里传入了 SIZES

@app.route('/upload', methods=['POST'])
def upload_image():
    if 'file' not in request.files:
        return render_template('index.html', error="请选择文件", sizes=SIZES)
    file = request.files['file']
    if file.filename == '':
        return render_template('index.html', error="未选择文件", sizes=SIZES)
    
    selected_sizes = request.form.getlist('sizes')
    if not selected_sizes:
        return render_template('index.html', error="请至少选择一个目标尺寸", sizes=SIZES)
    
    if file and allowed_file(file.filename):
        try:
            session_id = str(uuid.uuid4())
            session['image_id'] = session_id
            
            img = Image.open(file)
            saved_files = []
            selected_sizes_info = []
            
            for size_index in selected_sizes:
                size_index = int(size_index)
                size = SIZES[size_index]
                resized = img.resize(size, Image.Resampling.LANCZOS)
                save_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_{size_index}.png")
                resized.save(save_path, format='PNG')
                saved_files.append(save_path)
                selected_sizes_info.append(size)
            
            session['saved_files'] = saved_files
            return render_template('result.html', 
                                original_name=file.filename,
                                sizes=selected_sizes_info)
        except Exception as e:
            return render_template('index.html', error=str(e), sizes=SIZES)
    else:
        return render_template('index.html', error="不支持的文件类型", sizes=SIZES)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg'}

@app.route('/download/<int:size_index>')
def download_image(size_index):
    if 'saved_files' not in session:
        return redirect(url_for('index'))
    try:
        file_path = session['saved_files'][size_index]
        return send_file(
            file_path,
            mimetype='image/png',
            as_attachment=True,
            download_name=f'resized_{SIZES[size_index][0]}x{SIZES[size_index][1]}.png'
        )
    except Exception as e:
        return str(e), 400

@app.route('/convert_favicon', methods=['POST'])
def convert_favicon():
    if 'file' not in request.files:
        return render_template('index.html', error="请选择文件")
    file = request.files['file']
    if file.filename == '':
        return render_template('index.html', error="未选择文件")
    
    if file and allowed_file(file.filename):
        try:
            session_id = str(uuid.uuid4())
            session['favicon_id'] = session_id
            
            # 打开原始图片
            img = Image.open(file)
            # 转换为RGBA模式
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # 创建一个新的ICO文件
            ico_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_favicon.ico")
            
            # 生成多个尺寸的图标
            favicon_images = []
            for size in FAVICON_SIZES:
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                favicon_images.append(resized_img)
            
            # 保存为ICO文件
            favicon_images[0].save(
                ico_path,
                format='ICO',
                sizes=[(16,16), (32,32), (48,48)],
                append_images=favicon_images[1:]
            )
            
            session['favicon_file'] = ico_path
            # 同时保存一个预览用的PNG
            preview_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_favicon_preview.png")
            favicon_images[1].save(preview_path, format='PNG')  # 使用32x32尺寸作为预览
            session['favicon_preview'] = preview_path
            
            return render_template('result.html', 
                                original_name=file.filename,
                                favicon_preview=f'/favicon_preview/{session_id}')
        except Exception as e:
            return render_template('index.html', error=str(e))
    else:
        return render_template('index.html', error="不支持的文件类型")

@app.route('/favicon_preview/<session_id>')
def favicon_preview(session_id):
    if 'favicon_preview' not in session:
        return redirect(url_for('index'))
    try:
        return send_file(
            session['favicon_preview'],
            mimetype='image/png'
        )
    except Exception as e:
        return str(e), 400

@app.route('/download_favicon')
def download_favicon():
    if 'favicon_file' not in session:
        return redirect(url_for('index'))
    try:
        return send_file(
            session['favicon_file'],
            mimetype='image/x-icon',
            as_attachment=True,
            download_name='favicon.ico'
        )
    except Exception as e:
        return str(e), 400

def cleanup_old_files():
    """清理临时文件"""
    for filename in os.listdir(UPLOAD_FOLDER):
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        try:
            if os.path.getmtime(file_path) < time.time() - 3600:  # 1小时后删除
                os.remove(file_path)
        except:
            pass

@app.before_request
def before_request():
    cleanup_old_files()

if __name__ == '__main__':
    app.run(debug=True)
