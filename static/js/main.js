// 文件拖放处理
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    const dropZone = e.currentTarget;
    dropZone.classList.remove('drag-over');
    
    const fileInput = dropZone.querySelector('input[type="file"]');
    const files = e.dataTransfer.files;
    
    if (files.length > 0) {
        fileInput.files = files;
        validateFileType(fileInput);
    }
}

// 文件类型验证
function validateFileType(input) {
    const file = input.files[0];
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    const fileInput = input.parentElement;
    const errorDisplay = fileInput.querySelector('.error-message') || createErrorDisplay(fileInput);
    
    if (!file) {
        return;
    }

    if (!allowedTypes.includes(file.type)) {
        errorDisplay.textContent = '请选择PNG、JPG或JPEG格式的图片';
        errorDisplay.style.display = 'block';
        input.value = '';
        return;
    }

    // 清除错误信息
    errorDisplay.style.display = 'none';

    // 显示选中的文件名和预览
    updateFileDisplay(input, file);
}

// 创建错误显示元素
function createErrorDisplay(container) {
    const errorDisplay = document.createElement('div');
    errorDisplay.className = 'error-message';
    errorDisplay.style.display = 'none';
    container.appendChild(errorDisplay);
    return errorDisplay;
}

// 更新文件显示
function updateFileDisplay(input, file) {
    const container = input.parentElement;
    let fileDisplay = container.querySelector('.file-display');
    
    if (!fileDisplay) {
        fileDisplay = document.createElement('div');
        fileDisplay.className = 'file-display';
        container.appendChild(fileDisplay);
    }

    // 清除之前的内容
    fileDisplay.innerHTML = '';

    // 添加文件信息
    const fileInfo = document.createElement('div');
    fileInfo.className = 'file-info';
    fileInfo.innerHTML = `
        <span class="file-name">已选择: ${file.name}</span>
        <span class="file-size">(${formatFileSize(file.size)})</span>
    `;
    fileDisplay.appendChild(fileInfo);

    // 如果是图片，添加预览
    if (file.type.startsWith('image/')) {
        const preview = document.createElement('div');
        preview.className = 'file-preview';
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        img.onload = () => URL.revokeObjectURL(img.src);
        preview.appendChild(img);
        fileDisplay.appendChild(preview);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 表单提交处理
function handleFormSubmit(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    if (submitButton) {
        submitButton.innerHTML = '<span class="spinner"></span> 处理中...';
        submitButton.disabled = true;
    }

    // 30秒后如果还没有响应，恢复按钮状态
    setTimeout(() => {
        if (submitButton.disabled) {
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        }
    }, 30000);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 为所有文件输入区域添加拖放功能
    const fileInputs = document.querySelectorAll('.file-input');
    fileInputs.forEach(container => {
        container.addEventListener('dragover', handleDragOver);
        container.addEventListener('dragleave', handleDragLeave);
        container.addEventListener('drop', handleDrop);
        
        // 添加文件选择事件监听
        const input = container.querySelector('input[type="file"]');
        if (input) {
            input.addEventListener('change', () => validateFileType(input));
        }
    });
    
    // 为所有表单添加提交处理
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            handleFormSubmit(this);
        });
    });
});