body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    margin: 0;
    padding: 0;
    background: #f0f2f5;
    color: #1a1a1a;
    line-height: 1.6;
}

nav {
    padding: 1rem 2rem;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

nav h1 {
    margin: 0;
    font-size: 1.5rem;
    color: #1a1a1a;
}

nav a {
    color: #4a90e2;
    text-decoration: none;
    margin-left: 1rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

nav a:hover {
    color: #357abd;
}

.upload-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.tool-section {
    background: #ffffff;
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 12px;
    border: 1px solid #e1e4e8;
    transition: box-shadow 0.3s ease;
}

.tool-section:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.tool-section h3 {
    margin-top: 0;
    color: #1a1a1a;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.error {
    color: #dc3545;
    background: #fdf2f2;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

button {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(74,144,226,0.2);
}

button:hover {
    background: #357abd;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(74,144,226,0.3);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(74,144,226,0.2);
}

.size-options {
    margin: 1.5rem 0;
    padding: 1.5rem;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    background: #f8f9fa;
}

.size-option {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin: 0.5rem 0;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    background: #ffffff;
    border: 1px solid #e1e4e8;
}

.size-option:hover {
    background-color: #f8f9fa;
    border-color: #4a90e2;
}

.size-option input[type="checkbox"] {
    margin-right: 1rem;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.size-option span {
    font-size: 0.95rem;
    color: #1a1a1a;
}

.hint {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0.75rem 0;
    line-height: 1.5;
}

.file-input {
    margin: 1.5rem 0;
    padding: 2rem;
    border: 2px dashed #e1e4e8;
    border-radius: 8px;
    text-align: center;
    transition: all 0.2s ease;
    background: #ffffff;
    position: relative;
}

.file-input.drag-over {
    border-color: #4a90e2;
    background: #f8f9fa;
    transform: scale(1.01);
}

.file-input:hover {
    border-color: #4a90e2;
    background: #f8f9fa;
}

.file-input input[type="file"] {
    margin: 1rem 0;
    cursor: pointer;
}

.file-display {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    text-align: left;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.file-name {
    font-weight: 500;
    color: #1a1a1a;
}

.file-size {
    color: #6c757d;
    font-size: 0.9rem;
}

.file-preview {
    margin-top: 1rem;
    text-align: center;
}

.file-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-message {
    color: #dc3545;
    background: #fdf2f2;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin: 0.5rem 0;
    font-size: 0.95rem;
    display: none;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

.result-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.result-item {
    text-align: center;
    background: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e1e4e8;
    transition: all 0.2s ease;
}

.result-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transform: translateY(-2px);
}

.preview-image {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.download-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    margin-top: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.back-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: #6c757d;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    margin-top: 2rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.download-btn:hover, .back-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
