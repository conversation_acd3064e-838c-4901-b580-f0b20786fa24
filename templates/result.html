{% extends "base.html" %}
{% block title %}处理结果{% endblock %}

{% block content %}
<div class="result-container">
    <h2>处理完成</h2>
    <p>原始文件：{{ original_name }}</p>
    
    {% if sizes %}
    <div class="result-grid">
        {% for size in sizes %}
        <div class="result-item">
            <div class="size-info">
                <h3>{{ size[0] }}x{{ size[1] }}</h3>
                <a href="{{ url_for('download_image', size_index=loop.index0) }}" 
                   class="download-btn">下载</a>
            </div>
            <img src="{{ url_for('download_image', size_index=loop.index0) }}" 
                 alt="预览图 {{ size[0] }}x{{ size[1] }}"
                 class="preview-image">
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if favicon_preview %}
    <div class="favicon-result">
        <h3>Favicon 预览</h3>
        <div class="favicon-preview">
            <img src="{{ favicon_preview }}" alt="Favicon预览" class="favicon-image">
            <div class="favicon-info">
                <p>已生成包含16x16、32x32、48x48像素的favicon.ico文件</p>
                <a href="{{ url_for('download_favicon') }}" class="download-btn">下载favicon.ico</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    <a href="{{ url_for('index') }}" class="back-btn">返回上传</a>
</div>

<style>
.favicon-result {
    margin-top: 20px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
}

.favicon-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 10px;
}

.favicon-image {
    border: 1px solid #ddd;
    padding: 10px;
    background: white;
    border-radius: 4px;
}

.favicon-info {
    flex: 1;
}

.favicon-info p {
    margin-bottom: 10px;
    color: #666;
}
</style>
{% endblock %}
