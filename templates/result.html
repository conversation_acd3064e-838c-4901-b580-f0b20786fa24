<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转换结果 - 图片尺寸转换工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-align: center;
            padding: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .download-item {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .download-item:hover {
            border-color: #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .size-info h3 {
            margin: 0 0 5px 0;
            color: #495057;
        }
        .size-info p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9em;
        }
        .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            color: white;
            text-decoration: none;
        }
        .favicon-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        .favicon-preview {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-top: 15px;
        }
        .favicon-image {
            max-width: 64px;
            max-height: 64px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .back-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            color: white;
            text-decoration: none;
        }
        .text-center {
            text-align: center;
        }
        .hint {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ 转换完成</h1>
            <p>原文件：{{ original_name }}</p>
        </div>
        <div class="content">
            {% if sizes %}
            <h3>📥 下载转换后的图片</h3>
            {% for size in sizes %}
            <div class="download-item">
                <div class="size-info">
                    <h3>{{ size[0] }} × {{ size[1] }} 像素</h3>
                    <p>PNG格式，高质量压缩</p>
                </div>
                <a href="/download/{{ loop.index0 }}" class="download-btn">下载</a>
            </div>
            {% endfor %}
            {% endif %}

            {% if favicon_preview %}
            <div class="favicon-section">
                <h3>🌟 Favicon图标预览</h3>
                <div class="favicon-preview">
                    <img src="{{ favicon_preview }}" alt="Favicon预览" class="favicon-image">
                    <div>
                        <h4>Favicon.ico</h4>
                        <p>包含16×16、32×32、48×48三种尺寸</p>
                        <a href="/download_favicon" class="download-btn">下载ICO文件</a>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="text-center">
                <a href="/" class="back-btn">返回首页</a>
            </div>

            <p class="hint">💡 提示：转换后的文件将在1小时后自动删除</p>
        </div>
    </div>

</body>
</html>
