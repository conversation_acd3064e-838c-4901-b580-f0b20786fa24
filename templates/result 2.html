<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转换结果 - 图片尺寸转换工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
        }
        .card-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 20px;
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .download-item {
            margin: 15px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            background: white;
        }
        .download-item:hover {
            border-color: #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            max-width: 64px;
            max-height: 64px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2 class="mb-0">✅ 转换完成</h2>
                        <p class="mb-0">原文件：{{ original_name }}</p>
                    </div>
                    <div class="card-body p-4">
                        {% if sizes %}
                            <!-- 普通尺寸转换结果 -->
                            <h4 class="mb-3">📥 下载转换后的图片</h4>
                            {% for i, size in sizes|enumerate %}
                            <div class="download-item">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-1">{{ size[0] }} × {{ size[1] }} 像素</h5>
                                        <p class="text-muted mb-0">PNG格式，高质量压缩</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="/download/{{ i }}" class="btn btn-success">
                                            <i class="fas fa-download"></i> 下载
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% endif %}
                        
                        {% if favicon_preview %}
                            <!-- Favicon转换结果 -->
                            <h4 class="mb-3">🌟 Favicon图标预览</h4>
                            <div class="download-item">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <img src="{{ favicon_preview }}" alt="Favicon预览" class="favicon-preview">
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="mb-1">Favicon.ico</h5>
                                        <p class="text-muted mb-0">包含16×16、32×32、48×48三种尺寸</p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="/download_favicon" class="btn btn-success">
                                            <i class="fas fa-download"></i> 下载ICO文件
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <a href="/" class="btn btn-primary btn-lg">
                                <i class="fas fa-arrow-left"></i> 返回首页
                            </a>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                💡 提示：转换后的文件将在1小时后自动删除
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
