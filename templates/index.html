<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片尺寸转换工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .tool-section {
            background: #f8f9fa;
            padding: 25px;
            margin-bottom: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        .tool-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.5em;
        }
        .size-options {
            margin: 20px 0;
        }
        .size-option {
            display: block;
            margin: 10px 0;
            padding: 15px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .size-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .size-option input {
            margin-right: 10px;
        }
        .file-input {
            margin: 20px 0;
        }
        .file-input input[type="file"] {
            width: 100%;
            padding: 15px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            background: white;
            cursor: pointer;
        }
        .file-input input[type="file"]:hover {
            border-color: #667eea;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .hint {
            color: #6c757d;
            font-size: 0.9em;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 图片尺寸转换工具</h1>
            <p>支持PNG、JPG、JPEG格式，现在支持320×320尺寸！</p>
        </div>
        <div class="content">
            {% if error %}
            <div class="error">{{ error }}</div>
            {% endif %}

            <div class="tool-section">
                <h3>图片尺寸调整</h3>
                <form action="/upload" method="post" enctype="multipart/form-data">
                    <div class="size-options">
                        <h4>选择目标尺寸（可多选）：</h4>
                        {% for size in sizes %}
                        <label class="size-option">
                            <input type="checkbox" name="sizes" value="{{ loop.index0 }}">
                            <span>{{ size[0] }} × {{ size[1] }} 像素
                                {% if size[0] == 28 and size[1] == 28 %}
                                    （小图标）
                                {% elif size[0] == 108 and size[1] == 108 %}
                                    （中等图标）
                                {% elif size[0] == 320 and size[1] == 320 %}
                                    （大图标/头像）
                                {% endif %}
                            </span>
                        </label>
                        {% endfor %}
                    </div>
                    <div class="file-input">
                        <p class="hint">支持的格式：PNG, JPG, JPEG</p>
                        <input type="file" name="file" accept="image/*" required>
                    </div>
                    <button type="submit">上传并调整尺寸</button>
                </form>
            </div>

            <div class="tool-section">
                <h3>🌟 Favicon 转换</h3>
                <form action="/convert_favicon" method="post" enctype="multipart/form-data">
                    <div class="file-input">
                        <p class="hint">上传图片生成网站图标（favicon.ico）</p>
                        <p class="hint">将自动生成16×16、32×32、48×48像素的图标</p>
                        <input type="file" name="file" accept="image/*" required>
                    </div>
                    <button type="submit">生成Favicon</button>
                </form>
            </div>
        </div>
    </div>

</body>
</html>
