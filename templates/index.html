<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片尺寸转换工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            text-align: center;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        .alert {
            border-radius: 10px;
        }
        .size-option {
            margin: 10px 0;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .size-option:hover {
            border-color: #667eea;
            background-color: #f8f9fa;
        }
        .size-option input:checked + label {
            color: #667eea;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2 class="mb-0">🖼️ 图片尺寸转换工具</h2>
                        <p class="mb-0">支持PNG、JPG、JPEG格式</p>
                    </div>
                    <div class="card-body p-4">
                        {% if error %}
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> {{ error }}
                            </div>
                        {% endif %}
                        
                        <!-- 普通尺寸转换 -->
                        <form method="post" action="/upload" enctype="multipart/form-data">
                            <div class="mb-4">
                                <label for="file" class="form-label">选择图片文件</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".png,.jpg,.jpeg" required>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label">选择目标尺寸（可多选）</label>
                                {% for i, size in sizes|enumerate %}
                                <div class="size-option">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="sizes" value="{{ i }}" id="size{{ i }}">
                                        <label class="form-check-label" for="size{{ i }}">
                                            <strong>{{ size[0] }} × {{ size[1] }} 像素</strong>
                                            {% if size[0] == 28 and size[1] == 28 %}
                                                <small class="text-muted">（小图标）</small>
                                            {% elif size[0] == 108 and size[1] == 108 %}
                                                <small class="text-muted">（中等图标）</small>
                                            {% elif size[0] == 320 and size[1] == 320 %}
                                                <small class="text-muted">（大图标/头像）</small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic"></i> 开始转换
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-5">
                        
                        <!-- Favicon转换 -->
                        <div class="text-center mb-3">
                            <h4>🌟 Favicon图标转换</h4>
                            <p class="text-muted">将图片转换为网站图标（.ico格式）</p>
                        </div>
                        
                        <form method="post" action="/convert_favicon" enctype="multipart/form-data">
                            <div class="mb-4">
                                <label for="favicon_file" class="form-label">选择图片文件</label>
                                <input type="file" class="form-control" id="favicon_file" name="file" accept=".png,.jpg,.jpeg" required>
                                <div class="form-text">将自动生成16×16、32×32、48×48三种尺寸的图标</div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-star"></i> 转换为Favicon
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
